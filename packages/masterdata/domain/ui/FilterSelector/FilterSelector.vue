<template>
  <TsetDropdown v-data-test class="relative" ref="dropdown" @closed="onClosed">
  <template #toggle="{ toggle }">
    <FilterButton :button-style="props.buttonStyle" :state="state">
    <button
          v-data-test:toggle
          type="button"
          class="flex items-center gap-6 px-8 py-7 text-sm transition"
          @click="toggle"
        >
          <IconChangeFilter class="h-20 w-20" />
        </button>
      </FilterButton>
    </template>
    <template #dropdown-content>
      <TsetDropdownContent class="absolute right-0 z-1 w-300">
        <ul
          class="grid max-h-[min(340px,35vh)] grid-cols-[1fr_auto_auto] gap-x-8 gap-y-20 overflow-y-auto px-12 py-10"
        >
          <li class="text-body-light col-span-full grid grid-cols-[subgrid]">
            <!-- this is just a placeholder element to fill in the first cell of the grid -->
            <span></span>
            <span>{{ $t('statics.filter') }}</span>
            <span>{{ $t('statics.table') }}</span>
          </li>

          <li class="text-body-light col-span-full grid grid-cols-[subgrid]">
            <span>{{ $t('statics.toggleAll') }}</span>
            <TsetCheckbox
              class="justify-self-center"
              size="small"
              v-data-test:toggle-all-filters-checkbox
              :status="allFiltersStatus"
              :is-disabled="allFiltersAreNonToggleable"
              @click="toggleAllFilters"
            />
            <TsetCheckbox
              class="justify-self-center"
              size="small"
              v-data-test:toggle-all-columns-checkbox
              :status="allColumnsStatus"
              @click="toggleAllColumns"
            />
          </li>

          <li
            v-for="item in props.items"
            :key="item.key"
            v-data-test:item="item.key"
            class="text-body-light col-span-full grid grid-cols-[subgrid]"
          >
            {{ item.name }}
            <TsetCheckbox
              class="justify-self-center"
              size="small"
              v-data-test:toggle-filter-checkbox="item.key"
              :status="isVisibleFilter(item) ? 'checked' : 'unchecked'"
              :is-disabled="hasFilter(item)"
              @click="toggleFilter(item)"
            />
            <TsetCheckbox
              class="justify-self-center"
              size="small"
              v-data-test:toggle-column-checkbox="item.key"
              :status="isVisibleColumn(item) ? 'checked' : 'unchecked'"
              @click="toggleColumn(item)"
            />
          </li>
        </ul>
        <div v-data-test:filterButtons
             class="flex justify-end gap-10 border-t p-8">
          <TsetButton
            v-data-test:cancelButton
            :label="$t('actions.cancel')"
            type="secondary"
            @click="cancelSelection"
          />
          <TsetButton
            v-data-test:applyButton
            :label="$t('actions.apply')"
            @click="applySelection"
          />
        </div>
      </TsetDropdownContent>
    </template>
  </TsetDropdown>
</template>

<script setup lang="ts">
import type { SingleFilterDto } from '@domain/masterdata/model/openapi'
import type { FilterButtonStyle } from '@domain/masterdata/ui/shared/FilterButton/FilterButton'
import FilterButton from '@domain/masterdata/ui/shared/FilterButton/FilterButton.vue'
import TsetCheckbox from '@tset/design/atoms/TsetCheckbox/TsetCheckbox.vue'
import TsetDropdown from '@tset/design/organisms/TsetDropdown/TsetDropdown.vue'
import TsetDropdownContent from '@tset/design/organisms/TsetDropdown/TsetDropdownContent.vue'
import {computed, ref} from 'vue'
import TsetButton from "@tset/design/atoms/TsetButton";


//#region Type: Item
type Item = {
  key: string
  name: string
  filterVisible: boolean
  columnVisible: boolean
  filter: SingleFilterDto[] | undefined
}
const isVisibleFilter = (item: Item) => item.filterVisible
const isVisibleColumn = (item: Item) => item.columnVisible
const toKey = (item: Item) => item.key
const toKeys = (items: Item[]) => items.map(toKey)
const hasFilter = (item: Item) => Boolean(item.filter)
//#endregion Type: Item

//#region PROPS
const props = defineProps<{
  items: Item[]
  buttonStyle?: FilterButtonStyle
}>()
//#endregion PROPS

//#region EMITS
const emit = defineEmits<{
  'update:filters': [string[]]
  'update:columns': [string[]]
}>()
//#endregion EMITS

let initialItems = JSON.stringify(props.items)
const dropdown = ref<InstanceType<typeof TsetDropdown> | null>(null)
const state = ref<'Empty' | 'Filled' | 'Warning' >('Empty')

const visibleFilters = ref(props.items.filter(isVisibleFilter))

// Name might be a bit misleading, but I could not come up with anything better
// Name should imply that these filters cannot be toggled (because the checkbox is disabled)
const nonToggleableFilters = computed(() => props.items.filter(hasFilter))
const allFiltersAreNonToggleable = computed(() => props.items.every(hasFilter))

const allFiltersStatus = computed(() => {
  switch (visibleFilters.value.length) {
    case props.items.length:
      return 'checked'
    case 0:
      return 'unchecked'
    default:
      return 'indeterminate'
  }
})

const visibleColumns = ref(props.items.filter(isVisibleColumn))

const allColumnsStatus = computed(() => {
  switch (visibleColumns.value.length) {
    case props.items.length:
      return 'checked'
    case 0:
      return 'unchecked'
    default:
      return 'indeterminate'
  }
})

const syncState = () => {
  if (allFiltersStatus.value == 'unchecked' && allColumnsStatus.value == 'unchecked') {
    state.value = 'Empty'
  } else {
    state.value = 'Filled'
  }
}
syncState()

const applyFilters = () => {
  emit(
    'update:filters',
    toKeys(visibleFilters.value)
  )
}

const applyColumns = () => {
  emit(
    'update:columns', toKeys(visibleColumns.value)
  )
}

const applySelection = () => {
  applyFilters()
  applyColumns()
  initialItems = JSON.stringify(props.items)
  syncState()
  dropdown.value?.close()
}

const selectionHasChanged = () => {
  return initialItems !== JSON.stringify(props.items)
}

const onClosed = () => {
  if (selectionHasChanged()) {
    state.value = 'Warning'
  }
}

const syncValues = () => {
  if (selectionHasChanged()) {
    props.items.forEach(item => {
      const initialItem = JSON.parse(initialItems).find((initItem: Item) => item.key === initItem.key);
      if (item !== initialItem) {
        if (item.filterVisible !== initialItem.filterVisible) {
          toggleFilter(item)
        }
        if (item.columnVisible !== initialItem.columnVisible) {
          toggleColumn(item)
        }
      }
    });
  }
}

const cancelSelection = () => {
  syncValues()
  syncState()
  dropdown.value?.close()
}

function toggleFilter(item: Item) {
  item.filterVisible = !item.filterVisible;
  const index = visibleFilters.value.findIndex(i => i.key === item.key);
  if (index !== -1) {
    visibleFilters.value.splice(index, 1);
  } else {
    visibleFilters.value.push(item);
  }
}

function toggleAllFilters() {
  const status = allFiltersStatus.value === 'checked';
  props.items.forEach(item => {
    item["filterVisible"] = !status;
  });
  visibleFilters.value = status ? (nonToggleableFilters.value) : [...props.items];
}

function toggleColumn(item: Item) {
  item.columnVisible = !item.columnVisible;
  const index = visibleColumns.value.findIndex(i => i.key === item.key);
  if (index !== -1) {
    visibleColumns.value.splice(index, 1);
  } else {
    visibleColumns.value.push(item);
  }
}

function toggleAllColumns() {
  const status = allColumnsStatus.value === 'checked';
  props.items.forEach(item => {
    item["columnVisible"] = !status;
  });
  visibleColumns.value = status ? [] : [...props.items];
}

</script>

<script lang="ts">
export default {
  name: 'MasterdataFilterSelector',
}
</script>
