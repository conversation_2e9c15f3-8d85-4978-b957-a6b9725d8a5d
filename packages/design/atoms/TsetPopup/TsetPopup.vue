<template>
  <!-- teleport is a hack for pop-ups when they're obstructed by overflow:hidden parents
       e.g. see "calculation -> step -> cycle time table -> add step". It will look for the most
      top element without `overflow/overflowY: hidden` parents and teleport the popup there.
      -->
  <!-- tset-popup-opened attribute is a hack for adjusting parents z-index when needed  -->
  <Teleport v-if="teleport.target" :to="teleport.target">
    <div
      v-if="transition.isMounted"
      ref="selfElement"
      v-data-test
      class="tset-popup"
      :style="floatingStyles"
      tset-popup-opened
    >
      <TransitionComponent>
        <div
          v-if="popupState.isVisible"
          class="tset-popup__content"
          :class="{ 'no-shadow': noShadow }"
        >
          <slot></slot>
        </div>
      </TransitionComponent>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import {
  type Placement,
  offset as _offset,
  autoPlacement,
  autoUpdate,
  useFloating,
} from '@floating-ui/vue'
import {
  onClickOutside,
  useEventListener,
  useParentElement,
  useResizeObserver,
  watchImmediate,
} from '@vueuse/core'
import { computed, ref, watch } from 'vue'
import {
  getTransitionComponent,
  isFloatingObstructed,
  useMouseAnchor,
  usePopupContentVisibility,
  useTeleport,
  useTransitionState,
} from './useTsetPopup'

const props = withDefaults(
  defineProps<{
    offset?: [number, number]
    placements?: Placement[]
    openOnTriggerClick?: boolean
    closeOnClickOutside?: boolean
    closeOnSelfClick?: boolean
    opened?: boolean
    trigger?: HTMLElement | Element | 'parent'
    anchor?: 'mouse' | 'trigger'
    animation?: 'none' | 'fade' | 'slide-down' | 'slide-up'
    noShadow?: boolean
  }>(),
  {
    offset: () => [0, 0],
    placements: () => ['bottom-start', 'bottom-end', 'top-start', 'top-end'],
    openOnTriggerClick: true,
    closeOnClickOutside: true,
    closeOnSelfClick: true,
    trigger: 'parent',
    animation: 'fade',
    anchor: 'trigger',
    noShadow: false,
  }
)

const emit = defineEmits<{
  opened: [void]
  closed: [void]
  'update:opened': [boolean]
}>()

const popupState = usePopupContentVisibility(props.opened)
const teleport = useTeleport()

const transition = useTransitionState()
const TransitionComponent = getTransitionComponent(props.animation)

const parentElement = useParentElement()
const selfElement = ref<HTMLElement>()
const triggerElement = computed(() =>
  props.trigger === 'parent'
    ? (parentElement.value as HTMLElement)
    : props.trigger
)
const mouseAnchor = props.anchor === 'mouse' ? useMouseAnchor() : null
const anchorElement = mouseAnchor || triggerElement

function updateMouseAnchor(event: MouseEvent) {
  allowUnobstructedPositionSearch()
  mouseAnchor?.value.updatePosition(event)
  update()
}

defineExpose({
  updateMouseAnchor,
})

watchImmediate(() => props.opened, popupState.setVisibility)
watchImmediate(() => popupState.isVisible, toggleTransition)
watch(() => popupState.isVisible, emitEvents)
useEventListener(triggerElement, 'click', onOpenerClicked)
onClickOutside(selfElement, onOutsideClicked)

/* when content size has changed, it can become obstructed again */
useResizeObserver(selfElement, allowUnobstructedPositionSearch)

/**
 * this variable is needed to stop popup's teleporting too much
 * e.g. when it jumps out of scrollable page and starts fly over the
 * global header
 */
let isUnobstructedPositionFound = false
function allowUnobstructedPositionSearch() {
  isUnobstructedPositionFound = false
}
function unobstructedPositionFound() {
  isUnobstructedPositionFound = true
}
async function toggleTransition() {
  if (!triggerElement.value) {
    return
  }

  if (popupState.isVisible) {
    teleport.to(triggerElement.value)
    allowUnobstructedPositionSearch()
    transition.show()
  }

  await transition.waitForAnimationEnd()
  if (popupState.isHidden) {
    // Hide transition only after it's animation is finished
    transition.hide()
    // When popup is opened - it's teleported to the place where it's fully visible
    // When closing - teleport back to the initial location
    // to avoid situations when the target is no longer in DOM
    teleport.to(triggerElement.value)
  }
}

function emitEvents() {
  emit('update:opened', popupState.isVisible)
  if (popupState.isVisible) {
    emit('opened')
  } else {
    emit('closed')
  }
}

function onOpenerClicked(event: MouseEvent) {
  if (!props.openOnTriggerClick) {
    return
  }

  // when the popup is inside of the trigger
  if (isSelfClicked(event)) {
    if (props.closeOnSelfClick) {
      popupState.hide()
    }
  } else {
    popupState.toggle()
  }
}

function onOutsideClicked(event: MouseEvent) {
  if (!props.closeOnClickOutside) {
    return
  }

  if (isOpenerClicked(event)) {
    return
  }

  popupState.hide()
}

function isOpenerClicked(event: MouseEvent) {
  return triggerElement.value?.contains(event.target as Node)
}

function isSelfClicked(event: MouseEvent) {
  return selfElement.value?.contains(event.target as Node)
}

const { floatingStyles, update } = useFloating(anchorElement, selfElement, {
  whileElementsMounted: autoUpdate,
  middleware: [
    // offset should be the first (https://floating-ui.com/docs/offset)
    _offset({
      mainAxis: props.offset[1],
      crossAxis: props.offset[0],
    }),
    autoPlacement({
      allowedPlacements: props.placements,
    }),
    {
      name: 'moveAwayFromOverflows',
      fn: async (state) => {
        if (!transition.isMounted) {
          return state
        }

        // popup jumps higher in the DOM until it finds a place where it's no longer
        // half-hidden by it's parents or neighbors (e.g. with overflow: hidden, or higher z-index).
        if (
          isUnobstructedPositionFound ||
          teleport.target.tagName === 'DIALOG'
        ) {
          return state
        }
        if (await isFloatingObstructed(state)) {
          await teleport.toParent(update)
        } else {
          unobstructedPositionFound()
        }

        return state
      },
    },
  ],
})
</script>

<script lang="ts">
export default {
  name: 'TsetPopup',
}
</script>

<style scoped lang="postcss">
.tset-popup {
  @apply absolute left-0 top-0 z-9999 w-max;
}

.tset-popup__content {
  @apply rounded-md bg-white-default shadow-dropdown;

  &.no-shadow {
    @apply shadow-none;
  }
}

/* FIXME: remove this after all action menus are migrated to TsetPopup */
:deep(.action-menu) {
  @apply shadow-none;
}
</style>
