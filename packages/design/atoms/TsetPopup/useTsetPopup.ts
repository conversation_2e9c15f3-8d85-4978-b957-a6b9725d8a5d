import { detectOverflow, type MiddlewareState } from '@floating-ui/vue'
import {
  FadeTransition,
  SlideYDownTransition,
  SlideYUpTransition,
} from '@tset/shared-ui/vue-transitions/transitions'
import { useFlag } from '@tset/shared-utils/composables/useFlag'
import { useValue } from '@tset/shared-utils/composables/useValue'
import { isTestEnv } from '@tset/shared-utils/plugins/pluginUtils'
import { computed, reactive, ref, Transition } from 'vue'

const TRANSITION_DURATION = 300

// composable for managing the popup content visibility
function usePopupContentVisibility(def: boolean = false) {
  const { value: isVisible, off: hide, toggle: show } = useFlag(def)

  let nextVisibility = false
  const setVisibility = async (isOpened: boolean) => {
    // for quick open-close-open to keep the popup opened
    nextVisibility = isOpened
    const isClosing = isVisible.value && !isOpened
    if (isClosing) {
      // if we close popup too fast, nested vue events can be lost (e.g. menu item click)
      await waitForInsideEventsHandlers()
    }
    isVisible.value = nextVisibility
  }
  const toggle = () => (isVisible.value = !isVisible.value)

  return reactive({
    isVisible,
    isHidden: computed(() => !isVisible.value),
    hide,
    show,
    setVisibility,
    toggle,
  })
}

// we need to show transition before the content is mounted
// and hide after the content animation is finished
function useTransitionState() {
  const { value: isMounted, on: show, off: hide } = useFlag(false)

  return reactive({
    isMounted,
    show,
    hide,
    waitForAnimationEnd,
  })
}

// to dynamically select which animation to apply to the popup
function getTransitionComponent(
  animation: 'none' | 'fade' | 'slide-down' | 'slide-up'
) {
  return {
    none: Transition,
    fade: FadeTransition,
    'slide-down': SlideYDownTransition,
    'slide-up': SlideYUpTransition,
  }[animation]
}

// "virtual anchor" is being used when the popup should be shown
// at the mouse position (e.g. for context menus)
function useMouseAnchor() {
  const contextElement = document.body as Element

  const mouseAnchorEl = ref({
    mouseX: 0,
    mouseY: 0,
    getBoundingClientRect() {
      return {
        width: 0,
        height: 0,
        x: this.mouseX,
        y: this.mouseY,
        top: this.mouseY,
        left: this.mouseX,
        right: this.mouseX,
        bottom: this.mouseY,
      }
    },

    get contextElement() {
      return contextElement
    },

    updatePosition(event: MouseEvent) {
      mouseAnchorEl.value.mouseX = event.clientX
      mouseAnchorEl.value.mouseY = event.clientY
    },
  })

  return mouseAnchorEl
}

function useTeleport() {
  const [target, teleportTo] = useValue<Element>()

  async function toParent(cb?: () => void) {
    if (target.value.parentElement?.tagName === 'DIALOG') {
      teleportTo(target.value)
      cb?.()
    }
    if (target.value?.parentElement) {
      teleportTo(target.value.parentElement)
      cb?.()
    }
  }

  return reactive({ target, to: teleportTo, toParent })
}

async function isFloatingObstructed(state: MiddlewareState) {
  const { top, left, right, bottom } = await detectOverflow(state)
  return top > 0 || left > 0 || right > 0 || bottom > 0
}

async function waitForAnimationEnd() {
  if (!isTestEnv()) {
    await new Promise((resolve) => setTimeout(resolve, TRANSITION_DURATION))
  }
}

async function waitForInsideEventsHandlers() {
  await new Promise((resolve) => setTimeout(resolve, 0))
}

export {
  getTransitionComponent,
  isFloatingObstructed,
  useMouseAnchor,
  usePopupContentVisibility,
  useTeleport,
  useTransitionState,
}
